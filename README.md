# Resume Maker

An AI-powered resume builder application that helps users create professional resumes by answering a few simple questions. The application includes an AI assistant to help refine and improve the generated resume.

## Features

- User-friendly form to collect employment history and career objectives
- AI-powered resume generation
- Resume editing capabilities
- AI assistant for resume refinement
- Multiple export formats

## Tech Stack

- **Frontend**: React with Vite, Tailwind CSS
- **Backend**: FastAPI
- **AI Integration**: Placeholder for actual AI service integration

## Project Structure

```
ResumeMaker/
├── frontend/             # React frontend
│   ├── public/           # Static assets
│   └── src/              # React source code
│       ├── components/   # Reusable components
│       ├── pages/        # Page components
│       ├── context/      # React context (state management)
│       └── assets/       # Images and other assets
├── backend/              # FastAPI backend
│   ├── app/              # Application code
│   │   ├── routers/      # API routes
│   │   ├── models/       # Data models
│   │   ├── services/     # Business logic
│   │   └── ai/           # AI integration
│   └── requirements.txt  # Python dependencies
└── README.md             # Project documentation
```

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- Python (v3.8 or later)
- npm or yarn

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/resume-maker.git
   cd resume-maker
   ```

2. Set up the frontend:
   ```
   cd frontend
   npm install
   ```

3. Set up the backend:
   ```
   cd backend
   pip install -r requirements.txt
   ```

### Running the Application

1. Start the backend server:
   ```
   cd backend
   python run.py
   ```

2. Start the frontend development server:
   ```
   cd frontend
   npm run dev
   ```

3. Open your browser and navigate to `http://localhost:5173`

## API Documentation

Once the backend server is running, you can access the API documentation at `http://localhost:8000/docs`

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- This project was created as a demonstration of a full-stack application with AI integration.
- The AI components are placeholders that can be replaced with actual AI services in a production environment.
