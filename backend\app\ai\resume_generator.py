from typing import Dict, Any, List

class ResumeGenerator:
    """
    A class to generate resume content using AI.
    This is a placeholder for actual AI integration.
    """
    
    @staticmethod
    def generate_summary(data: Dict[str, Any]) -> str:
        """
        Generate a professional summary based on user data.
        
        Args:
            data: Dictionary containing user information
            
        Returns:
            A professional summary paragraph
        """
        name = data.get("full_name", "")
        experiences = data.get("experience", [])
        skills = data.get("skills", [])
        objective = data.get("objective", "")
        
        # Extract the most recent job title and company
        recent_title = ""
        recent_company = ""
        if experiences and len(experiences) > 0:
            recent_title = experiences[0].get("position", "")
            recent_company = experiences[0].get("company", "")
        
        # Extract top skills
        top_skills = [skill.get("name", "") for skill in skills[:3]]
        skills_text = ", ".join(top_skills)
        
        # Generate a basic summary
        if recent_title and recent_company:
            summary = f"Experienced {recent_title} with a proven track record at {recent_company}. "
        else:
            summary = f"Professional with a strong background in {skills_text}. "
            
        if objective:
            summary += f"{objective} "
            
        if skills_text:
            summary += f"Skilled in {skills_text}, with a passion for delivering high-quality results."
        
        return summary
    
    @staticmethod
    def enhance_experience(experience: Dict[str, Any]) -> List[str]:
        """
        Enhance job descriptions with more professional language.
        
        Args:
            experience: Dictionary containing job experience
            
        Returns:
            A list of enhanced bullet points
        """
        position = experience.get("position", "")
        company = experience.get("company", "")
        description = experience.get("description", "")
        achievements = experience.get("achievements", [])
        
        enhanced_bullets = []
        
        # Generate standard bullets based on position
        if "manager" in position.lower() or "director" in position.lower() or "lead" in position.lower():
            enhanced_bullets.append(f"Led cross-functional teams at {company} to deliver projects on time and within budget.")
            enhanced_bullets.append(f"Developed and implemented strategic initiatives that improved operational efficiency.")
            enhanced_bullets.append(f"Mentored team members and fostered a collaborative work environment.")
        
        elif "developer" in position.lower() or "engineer" in position.lower():
            enhanced_bullets.append(f"Developed and maintained software applications using industry best practices.")
            enhanced_bullets.append(f"Collaborated with cross-functional teams to implement new features and resolve issues.")
            enhanced_bullets.append(f"Participated in code reviews and contributed to technical documentation.")
        
        elif "designer" in position.lower():
            enhanced_bullets.append(f"Created visually appealing designs that aligned with brand guidelines and user expectations.")
            enhanced_bullets.append(f"Collaborated with stakeholders to understand requirements and deliver effective design solutions.")
            enhanced_bullets.append(f"Utilized design tools and techniques to create high-quality visual assets.")
        
        elif "sales" in position.lower() or "account" in position.lower():
            enhanced_bullets.append(f"Built and maintained strong relationships with clients to drive business growth.")
            enhanced_bullets.append(f"Identified new business opportunities and developed strategies to capitalize on them.")
            enhanced_bullets.append(f"Exceeded sales targets through effective communication and negotiation skills.")
        
        else:
            enhanced_bullets.append(f"Contributed to the success of {company} through dedication and professional excellence.")
            enhanced_bullets.append(f"Collaborated with team members to achieve organizational goals and objectives.")
            enhanced_bullets.append(f"Demonstrated strong problem-solving skills and attention to detail.")
        
        # Add any user-provided achievements
        if achievements:
            enhanced_bullets.extend(achievements)
        
        return enhanced_bullets[:5]  # Limit to 5 bullet points
