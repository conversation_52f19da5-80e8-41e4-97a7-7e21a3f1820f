from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import os

from app.routers import resume, ai_assistant

# Create FastAPI app
app = FastAPI(
    title="Resume Maker API",
    description="API for generating and managing resumes with AI assistance",
    version="0.1.0",
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(resume.router, prefix="/api/resume", tags=["Resume"])
app.include_router(ai_assistant.router, prefix="/api/assistant", tags=["AI Assistant"])

# Create a directory for static files if it doesn't exist
os.makedirs("static", exist_ok=True)
os.makedirs("static/resumes", exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "ok"}
