from fastapi import APIRouter, HTTPException, Body
from pydantic import BaseModel
from typing import List, Optional
import uuid
from datetime import datetime

router = APIRouter()

# Models
class Message(BaseModel):
    role: str  # "user" or "assistant"
    content: str
    timestamp: str

class Conversation(BaseModel):
    id: str
    messages: List[Message]
    created_at: str
    updated_at: str

class MessageInput(BaseModel):
    content: str

# In-memory storage (would be replaced with a database in production)
CONVERSATIONS = {}

@router.post("/chat/{conversation_id}", response_model=Message)
async def chat(conversation_id: str, message: MessageInput):
    """Send a message to the AI assistant"""
    if conversation_id not in CONVERSATIONS:
        # Try to create a new conversation if it doesn't exist
        now = datetime.now().isoformat()
        CONVERSATIONS[conversation_id] = Conversation(
            id=conversation_id,
            messages=[],
            created_at=now,
            updated_at=now
        )
    
    # Add user message to conversation
    now = datetime.now().isoformat()
    user_message = Message(
        role="user",
        content=message.content,
        timestamp=now
    )
    CONVERSATIONS[conversation_id].messages.append(user_message)
    
    # Generate AI response (placeholder for actual AI integration)
    ai_response = generate_ai_response(message.content)
    
    # Add AI message to conversation
    now = datetime.now().isoformat()
    ai_message = Message(
        role="assistant",
        content=ai_response,
        timestamp=now
    )
    CONVERSATIONS[conversation_id].messages.append(ai_message)
    
    # Update conversation timestamp
    CONVERSATIONS[conversation_id].updated_at = now
    
    return ai_message

@router.get("/conversations/{conversation_id}", response_model=Conversation)
async def get_conversation(conversation_id: str):
    """Get a conversation by ID"""
    if conversation_id not in CONVERSATIONS:
        raise HTTPException(status_code=404, detail="Conversation not found")
    
    return CONVERSATIONS[conversation_id]

@router.post("/conversations", response_model=Conversation)
async def create_conversation():
    """Create a new conversation"""
    conversation_id = str(uuid.uuid4())
    now = datetime.now().isoformat()
    
    CONVERSATIONS[conversation_id] = Conversation(
        id=conversation_id,
        messages=[],
        created_at=now,
        updated_at=now
    )
    
    return CONVERSATIONS[conversation_id]

def generate_ai_response(user_message: str) -> str:
    """
    Generate an AI response based on the user message.
    This is a placeholder for actual AI integration.
    """
    # Simple rule-based responses for demonstration
    user_message_lower = user_message.lower()
    
    if "hello" in user_message_lower or "hi" in user_message_lower:
        return "Hello! I'm your resume assistant. How can I help you today?"
    
    elif "resume" in user_message_lower and "help" in user_message_lower:
        return "I'd be happy to help with your resume. I can suggest improvements, help with formatting, or provide advice on content. What specific aspect of your resume would you like help with?"
    
    elif "experience" in user_message_lower:
        return "When describing your work experience, focus on achievements rather than just responsibilities. Use action verbs and quantify your accomplishments when possible. For example, instead of 'Responsible for sales', try 'Increased sales by 20% over 6 months'."
    
    elif "skill" in user_message_lower:
        return "For skills, prioritize those most relevant to the job you're applying for. Include both technical skills (like programming languages or software) and soft skills (like communication or leadership) that are backed up by your experience."
    
    elif "education" in user_message_lower:
        return "For your education section, include your degree, institution, graduation date, and relevant coursework or achievements. If you're a recent graduate, you might want to place this section before your work experience."
    
    elif "objective" in user_message_lower or "summary" in user_message_lower:
        return "A strong resume summary or objective statement should be concise (2-3 sentences) and highlight your most relevant skills and experience. Tailor it to each job application to show how you're a perfect fit for that specific role."
    
    elif "thank" in user_message_lower:
        return "You're welcome! If you have any other questions about your resume, feel free to ask."
    
    else:
        return "I'm here to help with your resume. I can provide advice on formatting, content, or specific sections like experience, skills, or education. What would you like assistance with?"
