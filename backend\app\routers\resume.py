from fastapi import APIRouter, HTTPException, Body
from pydantic import BaseModel
from typing import List, Optional
import uuid
import json
import os
from datetime import datetime

router = APIRouter()

# Models
class Education(BaseModel):
    institution: str
    degree: str
    field_of_study: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    description: Optional[str] = None

class Experience(BaseModel):
    company: str
    position: str
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    description: Optional[str] = None
    achievements: Optional[List[str]] = None

class Skill(BaseModel):
    name: str
    level: Optional[str] = None

class ResumeInput(BaseModel):
    full_name: str
    email: str
    phone: Optional[str] = None
    location: Optional[str] = None
    objective: Optional[str] = None
    summary: Optional[str] = None
    education: List[Education] = []
    experience: List[Experience] = []
    skills: List[Skill] = []

class Resume(ResumeInput):
    id: str
    created_at: str
    updated_at: str

# In-memory storage (would be replaced with a database in production)
RESUMES = {}

@router.post("/generate", response_model=Resume)
async def generate_resume(resume_input: ResumeInput):
    """Generate a new resume based on user input"""
    # Generate a unique ID for the resume
    resume_id = str(uuid.uuid4())
    now = datetime.now().isoformat()
    
    # Create a new resume
    resume = Resume(
        id=resume_id,
        created_at=now,
        updated_at=now,
        **resume_input.dict()
    )
    
    # Store the resume
    RESUMES[resume_id] = resume
    
    # Save to file (simulating database storage)
    os.makedirs("static/resumes", exist_ok=True)
    with open(f"static/resumes/{resume_id}.json", "w") as f:
        json.dump(resume.dict(), f, indent=2)
    
    return resume

@router.get("/{resume_id}", response_model=Resume)
async def get_resume(resume_id: str):
    """Get a resume by ID"""
    if resume_id not in RESUMES:
        # Try to load from file
        try:
            with open(f"static/resumes/{resume_id}.json", "r") as f:
                resume_data = json.load(f)
                RESUMES[resume_id] = Resume(**resume_data)
        except FileNotFoundError:
            raise HTTPException(status_code=404, detail="Resume not found")
    
    return RESUMES[resume_id]

@router.put("/{resume_id}", response_model=Resume)
async def update_resume(resume_id: str, resume_input: ResumeInput):
    """Update an existing resume"""
    if resume_id not in RESUMES:
        # Try to load from file
        try:
            with open(f"static/resumes/{resume_id}.json", "r") as f:
                resume_data = json.load(f)
                RESUMES[resume_id] = Resume(**resume_data)
        except FileNotFoundError:
            raise HTTPException(status_code=404, detail="Resume not found")
    
    # Update the resume
    now = datetime.now().isoformat()
    resume = Resume(
        id=resume_id,
        created_at=RESUMES[resume_id].created_at,
        updated_at=now,
        **resume_input.dict()
    )
    
    # Store the updated resume
    RESUMES[resume_id] = resume
    
    # Save to file
    with open(f"static/resumes/{resume_id}.json", "w") as f:
        json.dump(resume.dict(), f, indent=2)
    
    return resume
