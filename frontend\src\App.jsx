import React, { useState } from 'react';
import ResumeForm from './components/ResumeForm';

function App() {
  const [showForm, setShowForm] = useState(false);

  return (
    <div className="min-h-screen bg-gray-100 p-4 md:p-8">
      {!showForm ? (
        // Landing page
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-blue-600 mb-4">Resume Maker</h1>
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-2">Create Your Professional Resume</h2>
            <p className="text-gray-600 mb-4">
              Answer a few simple questions and our AI-powered resume builder will create a tailored,
              professional resume that stands out to employers.
            </p>
            <button
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
              onClick={() => setShowForm(true)}
            >
              Get Started
            </button>
          </div>
        </div>
      ) : (
        // Resume form
        <ResumeForm />
      )}
    </div>
  );
}

export default App;
