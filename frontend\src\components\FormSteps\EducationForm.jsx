import React, { useState } from 'react';

const EducationForm = ({ education, handleArrayChange, nextStep, prevStep }) => {
  const [educationList, setEducationList] = useState(education.length > 0 ? education : []);
  const [currentEducation, setCurrentEducation] = useState({
    institution: '',
    degree: '',
    field_of_study: '',
    start_date: '',
    end_date: '',
    description: ''
  });
  const [isEditing, setIsEditing] = useState(false);
  const [editIndex, setEditIndex] = useState(null);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCurrentEducation({
      ...currentEducation,
      [name]: value
    });
  };

  const handleAddEducation = () => {
    // Basic validation
    if (!currentEducation.institution || !currentEducation.degree) {
      alert('Please fill in at least the institution and degree fields');
      return;
    }

    if (isEditing && editIndex !== null) {
      // Update existing education entry
      const updatedList = [...educationList];
      updatedList[editIndex] = currentEducation;
      setEducationList(updatedList);
      setIsEditing(false);
      setEditIndex(null);
    } else {
      // Add new education entry
      setEducationList([...educationList, currentEducation]);
    }

    // Reset form
    setCurrentEducation({
      institution: '',
      degree: '',
      field_of_study: '',
      start_date: '',
      end_date: '',
      description: ''
    });
  };

  const handleEditEducation = (index) => {
    setCurrentEducation(educationList[index]);
    setIsEditing(true);
    setEditIndex(index);
  };

  const handleRemoveEducation = (index) => {
    const updatedList = educationList.filter((_, i) => i !== index);
    setEducationList(updatedList);
  };

  const handleNext = () => {
    handleArrayChange(educationList);
    nextStep();
  };

  const handlePrev = () => {
    handleArrayChange(educationList);
    prevStep();
  };

  return (
    <div>
      <h3 className="text-lg font-semibold mb-4">Education</h3>
      
      {/* Education list */}
      {educationList.length > 0 && (
        <div className="mb-6">
          <h4 className="text-md font-medium mb-2">Your Education</h4>
          <div className="space-y-3">
            {educationList.map((edu, index) => (
              <div key={index} className="p-3 border rounded-md bg-gray-50">
                <div className="flex justify-between">
                  <div>
                    <p className="font-semibold">{edu.degree}</p>
                    <p>{edu.institution}</p>
                    {edu.start_date && edu.end_date && (
                      <p className="text-sm text-gray-600">
                        {edu.start_date} - {edu.end_date}
                      </p>
                    )}
                  </div>
                  <div>
                    <button
                      type="button"
                      onClick={() => handleEditEducation(index)}
                      className="text-blue-500 hover:text-blue-700 mr-2"
                    >
                      Edit
                    </button>
                    <button
                      type="button"
                      onClick={() => handleRemoveEducation(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      Remove
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Education form */}
      <div className="mb-6 p-4 border rounded-md">
        <h4 className="text-md font-medium mb-3">
          {isEditing ? 'Edit Education' : 'Add Education'}
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="institution">
              Institution <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="institution"
              name="institution"
              value={currentEducation.institution}
              onChange={handleInputChange}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              placeholder="University Name"
            />
          </div>
          
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="degree">
              Degree <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="degree"
              name="degree"
              value={currentEducation.degree}
              onChange={handleInputChange}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              placeholder="Bachelor of Science"
            />
          </div>
        </div>
        
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="field_of_study">
            Field of Study
          </label>
          <input
            type="text"
            id="field_of_study"
            name="field_of_study"
            value={currentEducation.field_of_study}
            onChange={handleInputChange}
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            placeholder="Computer Science"
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="start_date">
              Start Date
            </label>
            <input
              type="month"
              id="start_date"
              name="start_date"
              value={currentEducation.start_date}
              onChange={handleInputChange}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            />
          </div>
          
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="end_date">
              End Date
            </label>
            <input
              type="month"
              id="end_date"
              name="end_date"
              value={currentEducation.end_date}
              onChange={handleInputChange}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            />
          </div>
        </div>
        
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="description">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            value={currentEducation.description}
            onChange={handleInputChange}
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            rows="3"
            placeholder="Describe your studies, achievements, etc."
          ></textarea>
        </div>
        
        <button
          type="button"
          onClick={handleAddEducation}
          className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          {isEditing ? 'Update Education' : 'Add Education'}
        </button>
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between">
        <button
          type="button"
          onClick={handlePrev}
          className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Previous
        </button>
        <button
          type="button"
          onClick={handleNext}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default EducationForm;
