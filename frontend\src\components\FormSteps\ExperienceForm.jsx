import React, { useState } from 'react';

const ExperienceForm = ({ experience, handleArrayChange, nextStep, prevStep }) => {
  const [experienceList, setExperienceList] = useState(experience.length > 0 ? experience : []);
  const [currentExperience, setCurrentExperience] = useState({
    company: '',
    position: '',
    start_date: '',
    end_date: '',
    description: '',
    achievements: []
  });
  const [achievement, setAchievement] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [editIndex, setEditIndex] = useState(null);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCurrentExperience({
      ...currentExperience,
      [name]: value
    });
  };

  const handleAchievementChange = (e) => {
    setAchievement(e.target.value);
  };

  const handleAddAchievement = () => {
    if (achievement.trim() === '') return;
    
    setCurrentExperience({
      ...currentExperience,
      achievements: [...currentExperience.achievements, achievement]
    });
    
    setAchievement('');
  };

  const handleRemoveAchievement = (index) => {
    const updatedAchievements = currentExperience.achievements.filter((_, i) => i !== index);
    setCurrentExperience({
      ...currentExperience,
      achievements: updatedAchievements
    });
  };

  const handleAddExperience = () => {
    // Basic validation
    if (!currentExperience.company || !currentExperience.position) {
      alert('Please fill in at least the company and position fields');
      return;
    }

    if (isEditing && editIndex !== null) {
      // Update existing experience entry
      const updatedList = [...experienceList];
      updatedList[editIndex] = currentExperience;
      setExperienceList(updatedList);
      setIsEditing(false);
      setEditIndex(null);
    } else {
      // Add new experience entry
      setExperienceList([...experienceList, currentExperience]);
    }

    // Reset form
    setCurrentExperience({
      company: '',
      position: '',
      start_date: '',
      end_date: '',
      description: '',
      achievements: []
    });
  };

  const handleEditExperience = (index) => {
    setCurrentExperience(experienceList[index]);
    setIsEditing(true);
    setEditIndex(index);
  };

  const handleRemoveExperience = (index) => {
    const updatedList = experienceList.filter((_, i) => i !== index);
    setExperienceList(updatedList);
  };

  const handleNext = () => {
    handleArrayChange(experienceList);
    nextStep();
  };

  const handlePrev = () => {
    handleArrayChange(experienceList);
    prevStep();
  };

  return (
    <div>
      <h3 className="text-lg font-semibold mb-4">Work Experience</h3>
      
      {/* Experience list */}
      {experienceList.length > 0 && (
        <div className="mb-6">
          <h4 className="text-md font-medium mb-2">Your Work Experience</h4>
          <div className="space-y-3">
            {experienceList.map((exp, index) => (
              <div key={index} className="p-3 border rounded-md bg-gray-50">
                <div className="flex justify-between">
                  <div>
                    <p className="font-semibold">{exp.position}</p>
                    <p>{exp.company}</p>
                    {exp.start_date && exp.end_date && (
                      <p className="text-sm text-gray-600">
                        {exp.start_date} - {exp.end_date}
                      </p>
                    )}
                  </div>
                  <div>
                    <button
                      type="button"
                      onClick={() => handleEditExperience(index)}
                      className="text-blue-500 hover:text-blue-700 mr-2"
                    >
                      Edit
                    </button>
                    <button
                      type="button"
                      onClick={() => handleRemoveExperience(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      Remove
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Experience form */}
      <div className="mb-6 p-4 border rounded-md">
        <h4 className="text-md font-medium mb-3">
          {isEditing ? 'Edit Experience' : 'Add Experience'}
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="company">
              Company <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="company"
              name="company"
              value={currentExperience.company}
              onChange={handleInputChange}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              placeholder="Company Name"
            />
          </div>
          
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="position">
              Position <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="position"
              name="position"
              value={currentExperience.position}
              onChange={handleInputChange}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              placeholder="Job Title"
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="start_date">
              Start Date
            </label>
            <input
              type="month"
              id="start_date"
              name="start_date"
              value={currentExperience.start_date}
              onChange={handleInputChange}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            />
          </div>
          
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="end_date">
              End Date
            </label>
            <input
              type="month"
              id="end_date"
              name="end_date"
              value={currentExperience.end_date}
              onChange={handleInputChange}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            />
          </div>
        </div>
        
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="description">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            value={currentExperience.description}
            onChange={handleInputChange}
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            rows="3"
            placeholder="Describe your role and responsibilities"
          ></textarea>
        </div>
        
        {/* Achievements section */}
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2">
            Achievements
          </label>
          
          <div className="flex mb-2">
            <input
              type="text"
              value={achievement}
              onChange={handleAchievementChange}
              className="shadow appearance-none border rounded-l w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              placeholder="Add an achievement or key responsibility"
            />
            <button
              type="button"
              onClick={handleAddAchievement}
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-r focus:outline-none focus:shadow-outline"
            >
              Add
            </button>
          </div>
          
          {currentExperience.achievements.length > 0 && (
            <ul className="list-disc pl-5 space-y-1">
              {currentExperience.achievements.map((item, index) => (
                <li key={index} className="flex justify-between items-center">
                  <span>{item}</span>
                  <button
                    type="button"
                    onClick={() => handleRemoveAchievement(index)}
                    className="text-red-500 hover:text-red-700 text-sm"
                  >
                    Remove
                  </button>
                </li>
              ))}
            </ul>
          )}
        </div>
        
        <button
          type="button"
          onClick={handleAddExperience}
          className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          {isEditing ? 'Update Experience' : 'Add Experience'}
        </button>
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between">
        <button
          type="button"
          onClick={handlePrev}
          className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Previous
        </button>
        <button
          type="button"
          onClick={handleNext}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default ExperienceForm;
