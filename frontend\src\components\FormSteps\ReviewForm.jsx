import React from 'react';

const ReviewForm = ({ formData, prevStep, handleSubmit }) => {
  return (
    <div>
      <h3 className="text-lg font-semibold mb-4">Review Your Information</h3>
      
      {/* Personal Information */}
      <div className="mb-6">
        <h4 className="text-md font-medium mb-2 border-b pb-1">Personal Information</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-semibold">Full Name:</p>
            <p>{formData.full_name}</p>
          </div>
          <div>
            <p className="text-sm font-semibold">Email:</p>
            <p>{formData.email}</p>
          </div>
          {formData.phone && (
            <div>
              <p className="text-sm font-semibold">Phone:</p>
              <p>{formData.phone}</p>
            </div>
          )}
          {formData.location && (
            <div>
              <p className="text-sm font-semibold">Location:</p>
              <p>{formData.location}</p>
            </div>
          )}
        </div>
      </div>
      
      {/* Summary & Objective */}
      {(formData.objective || formData.summary) && (
        <div className="mb-6">
          <h4 className="text-md font-medium mb-2 border-b pb-1">Summary & Objective</h4>
          {formData.objective && (
            <div className="mb-3">
              <p className="text-sm font-semibold">Career Objective:</p>
              <p>{formData.objective}</p>
            </div>
          )}
          {formData.summary && (
            <div>
              <p className="text-sm font-semibold">Professional Summary:</p>
              <p>{formData.summary}</p>
            </div>
          )}
        </div>
      )}
      
      {/* Education */}
      {formData.education.length > 0 && (
        <div className="mb-6">
          <h4 className="text-md font-medium mb-2 border-b pb-1">Education</h4>
          <div className="space-y-3">
            {formData.education.map((edu, index) => (
              <div key={index} className="p-3 border rounded-md bg-gray-50">
                <p className="font-semibold">{edu.degree}</p>
                <p>{edu.institution}</p>
                {edu.field_of_study && <p className="text-sm">{edu.field_of_study}</p>}
                {edu.start_date && edu.end_date && (
                  <p className="text-sm text-gray-600">
                    {edu.start_date} - {edu.end_date}
                  </p>
                )}
                {edu.description && <p className="text-sm mt-1">{edu.description}</p>}
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* Experience */}
      {formData.experience.length > 0 && (
        <div className="mb-6">
          <h4 className="text-md font-medium mb-2 border-b pb-1">Work Experience</h4>
          <div className="space-y-3">
            {formData.experience.map((exp, index) => (
              <div key={index} className="p-3 border rounded-md bg-gray-50">
                <p className="font-semibold">{exp.position}</p>
                <p>{exp.company}</p>
                {exp.start_date && exp.end_date && (
                  <p className="text-sm text-gray-600">
                    {exp.start_date} - {exp.end_date}
                  </p>
                )}
                {exp.description && <p className="text-sm mt-1">{exp.description}</p>}
                {exp.achievements && exp.achievements.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm font-semibold">Key Achievements:</p>
                    <ul className="list-disc pl-5 text-sm">
                      {exp.achievements.map((achievement, i) => (
                        <li key={i}>{achievement}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* Skills */}
      {formData.skills.length > 0 && (
        <div className="mb-6">
          <h4 className="text-md font-medium mb-2 border-b pb-1">Skills</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
            {formData.skills.map((skill, index) => (
              <div key={index} className="p-2 border rounded-md bg-gray-50">
                <p className="font-semibold">{skill.name}</p>
                <p className="text-sm text-gray-600">{skill.level}</p>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* Navigation and Submit buttons */}
      <div className="flex justify-between mt-8">
        <button
          type="button"
          onClick={prevStep}
          className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Previous
        </button>
        <button
          type="button"
          onClick={handleSubmit}
          className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Generate Resume
        </button>
      </div>
    </div>
  );
};

export default ReviewForm;
