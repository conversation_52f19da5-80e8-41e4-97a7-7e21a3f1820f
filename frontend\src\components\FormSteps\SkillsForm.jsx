import React, { useState } from 'react';

const SkillsForm = ({ skills, handleArrayChange, nextStep, prevStep }) => {
  const [skillsList, setSkillsList] = useState(skills.length > 0 ? skills : []);
  const [currentSkill, setCurrentSkill] = useState({
    name: '',
    level: 'Beginner' // Default level
  });
  const [isEditing, setIsEditing] = useState(false);
  const [editIndex, setEditIndex] = useState(null);

  const skillLevels = ['Beginner', 'Intermediate', 'Advanced', 'Expert'];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCurrentSkill({
      ...currentSkill,
      [name]: value
    });
  };

  const handleAddSkill = () => {
    // Basic validation
    if (!currentSkill.name) {
      alert('Please enter a skill name');
      return;
    }

    if (isEditing && editIndex !== null) {
      // Update existing skill
      const updatedList = [...skillsList];
      updatedList[editIndex] = currentSkill;
      setSkillsList(updatedList);
      setIsEditing(false);
      setEditIndex(null);
    } else {
      // Add new skill
      setSkillsList([...skillsList, currentSkill]);
    }

    // Reset form
    setCurrentSkill({
      name: '',
      level: 'Beginner'
    });
  };

  const handleEditSkill = (index) => {
    setCurrentSkill(skillsList[index]);
    setIsEditing(true);
    setEditIndex(index);
  };

  const handleRemoveSkill = (index) => {
    const updatedList = skillsList.filter((_, i) => i !== index);
    setSkillsList(updatedList);
  };

  const handleNext = () => {
    handleArrayChange(skillsList);
    nextStep();
  };

  const handlePrev = () => {
    handleArrayChange(skillsList);
    prevStep();
  };

  return (
    <div>
      <h3 className="text-lg font-semibold mb-4">Skills</h3>
      
      {/* Skills list */}
      {skillsList.length > 0 && (
        <div className="mb-6">
          <h4 className="text-md font-medium mb-2">Your Skills</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {skillsList.map((skill, index) => (
              <div key={index} className="p-3 border rounded-md bg-gray-50 flex justify-between items-center">
                <div>
                  <p className="font-semibold">{skill.name}</p>
                  <p className="text-sm text-gray-600">{skill.level}</p>
                </div>
                <div>
                  <button
                    type="button"
                    onClick={() => handleEditSkill(index)}
                    className="text-blue-500 hover:text-blue-700 mr-2"
                  >
                    Edit
                  </button>
                  <button
                    type="button"
                    onClick={() => handleRemoveSkill(index)}
                    className="text-red-500 hover:text-red-700"
                  >
                    Remove
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Skill form */}
      <div className="mb-6 p-4 border rounded-md">
        <h4 className="text-md font-medium mb-3">
          {isEditing ? 'Edit Skill' : 'Add Skill'}
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="name">
              Skill Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={currentSkill.name}
              onChange={handleInputChange}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              placeholder="e.g., JavaScript, Project Management, etc."
            />
          </div>
          
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="level">
              Proficiency Level
            </label>
            <select
              id="level"
              name="level"
              value={currentSkill.level}
              onChange={handleInputChange}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            >
              {skillLevels.map((level) => (
                <option key={level} value={level}>
                  {level}
                </option>
              ))}
            </select>
          </div>
        </div>
        
        <button
          type="button"
          onClick={handleAddSkill}
          className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          {isEditing ? 'Update Skill' : 'Add Skill'}
        </button>
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between">
        <button
          type="button"
          onClick={handlePrev}
          className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Previous
        </button>
        <button
          type="button"
          onClick={handleNext}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default SkillsForm;
