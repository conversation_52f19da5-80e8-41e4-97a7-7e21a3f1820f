import React from 'react';

const SummaryForm = ({ formData, handleChange, nextStep, prevStep }) => {
  const handleNext = (e) => {
    e.preventDefault();
    nextStep();
  };

  return (
    <form onSubmit={handleNext}>
      <h3 className="text-lg font-semibold mb-4">Summary & Objective</h3>
      
      <div className="mb-4">
        <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="objective">
          Career Objective
        </label>
        <textarea
          id="objective"
          name="objective"
          value={formData.objective}
          onChange={handleChange}
          className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          rows="3"
          placeholder="A brief statement about your career goals and what you're looking for"
        ></textarea>
        <p className="text-sm text-gray-500 mt-1">
          Example: "Seeking a challenging position as a Software Developer where I can utilize my skills in JavaScript and React to contribute to innovative projects."
        </p>
      </div>

      <div className="mb-6">
        <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="summary">
          Professional Summary
        </label>
        <textarea
          id="summary"
          name="summary"
          value={formData.summary}
          onChange={handleChange}
          className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          rows="5"
          placeholder="A summary of your professional background, skills, and achievements"
        ></textarea>
        <p className="text-sm text-gray-500 mt-1">
          Example: "Results-driven software developer with 5+ years of experience in building responsive web applications using modern JavaScript frameworks. Proven track record of delivering high-quality code and collaborating effectively in cross-functional teams."
        </p>
      </div>

      <div className="flex justify-between">
        <button
          type="button"
          onClick={prevStep}
          className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Previous
        </button>
        <button
          type="submit"
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Next
        </button>
      </div>
    </form>
  );
};

export default SummaryForm;
