import React, { useState } from 'react';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="bg-white shadow-sm">
      <div className="container-custom py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <span className="text-2xl font-bold text-primary">ResumeMaker</span>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <a href="#" className="text-text hover:text-primary transition-colors">Home</a>
            <a href="#" className="text-text hover:text-primary transition-colors">Features</a>
            <a href="#" className="text-text hover:text-primary transition-colors">Templates</a>
            <a href="#" className="text-text hover:text-primary transition-colors">Pricing</a>
            <button className="btn btn-primary">Get Started</button>
          </nav>

          {/* Mobile Menu Button */}
          <button 
            className="md:hidden p-2 rounded-md text-gray-600 hover:text-primary hover:bg-gray-100"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden mt-4 pb-4">
            <nav className="flex flex-col space-y-4">
              <a href="#" className="text-text hover:text-primary transition-colors">Home</a>
              <a href="#" className="text-text hover:text-primary transition-colors">Features</a>
              <a href="#" className="text-text hover:text-primary transition-colors">Templates</a>
              <a href="#" className="text-text hover:text-primary transition-colors">Pricing</a>
              <button className="btn btn-primary w-full">Get Started</button>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
