import React, { useState } from 'react';
import { resumeApi } from '../services/api';

// Form steps components
import PersonalInfoForm from './FormSteps/PersonalInfoForm';
import EducationForm from './FormSteps/EducationForm';
import ExperienceForm from './FormSteps/ExperienceForm';
import SkillsForm from './FormSteps/SkillsForm';
import SummaryForm from './FormSteps/SummaryForm';
import ReviewForm from './FormSteps/ReviewForm';

const ResumeForm = () => {
  // Form state
  const [formData, setFormData] = useState({
    full_name: '',
    email: '',
    phone: '',
    location: '',
    objective: '',
    summary: '',
    education: [],
    experience: [],
    skills: []
  });

  // Current step state
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 6;

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle education, experience, and skills arrays
  const handleArrayChange = (type, data) => {
    setFormData({
      ...formData,
      [type]: data
    });
  };

  // Navigate to next step
  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  // Navigate to previous step
  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      const data = await resumeApi.generateResume(formData);
      console.log('Resume generated:', data);

      // Here you would typically redirect to a success page or the resume view
      alert('Resume created successfully!');

    } catch (error) {
      console.error('Error generating resume:', error);
      alert('Failed to create resume. Please try again.');
    }
  };

  // Render the current step
  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <PersonalInfoForm
            formData={formData}
            handleChange={handleChange}
            nextStep={nextStep}
          />
        );
      case 2:
        return (
          <EducationForm
            education={formData.education}
            handleArrayChange={(data) => handleArrayChange('education', data)}
            nextStep={nextStep}
            prevStep={prevStep}
          />
        );
      case 3:
        return (
          <ExperienceForm
            experience={formData.experience}
            handleArrayChange={(data) => handleArrayChange('experience', data)}
            nextStep={nextStep}
            prevStep={prevStep}
          />
        );
      case 4:
        return (
          <SkillsForm
            skills={formData.skills}
            handleArrayChange={(data) => handleArrayChange('skills', data)}
            nextStep={nextStep}
            prevStep={prevStep}
          />
        );
      case 5:
        return (
          <SummaryForm
            formData={formData}
            handleChange={handleChange}
            nextStep={nextStep}
            prevStep={prevStep}
          />
        );
      case 6:
        return (
          <ReviewForm
            formData={formData}
            prevStep={prevStep}
            handleSubmit={handleSubmit}
          />
        );
      default:
        return null;
    }
  };

  // Progress bar calculation
  const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;

  return (
    <div className="max-w-3xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold text-center mb-6">Create Your Resume</h2>

      {/* Progress bar */}
      <div className="w-full bg-gray-200 rounded-full h-2.5 mb-6">
        <div
          className="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
          style={{ width: `${progress}%` }}
        ></div>
      </div>

      {/* Step indicator */}
      <div className="flex justify-between mb-8">
        <div className="text-sm">Step {currentStep} of {totalSteps}</div>
        <div className="text-sm font-medium">
          {currentStep === 1 && 'Personal Information'}
          {currentStep === 2 && 'Education'}
          {currentStep === 3 && 'Work Experience'}
          {currentStep === 4 && 'Skills'}
          {currentStep === 5 && 'Summary & Objective'}
          {currentStep === 6 && 'Review'}
        </div>
      </div>

      {/* Form steps */}
      <div className="mb-6">
        {renderStep()}
      </div>
    </div>
  );
};

export default ResumeForm;
