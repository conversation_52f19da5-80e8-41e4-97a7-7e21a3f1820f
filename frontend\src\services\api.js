// API service for communicating with the backend

const API_BASE_URL = 'http://localhost:8000/api';

// Resume API endpoints
export const resumeApi = {
  // Generate a new resume
  generateResume: async (resumeData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/resume/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(resumeData),
      });
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error generating resume:', error);
      throw error;
    }
  },
  
  // Get a resume by ID
  getResume: async (resumeId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/resume/${resumeId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching resume:', error);
      throw error;
    }
  },
  
  // Update an existing resume
  updateResume: async (resumeId, resumeData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/resume/${resumeId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(resumeData),
      });
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error updating resume:', error);
      throw error;
    }
  },
};

// AI Assistant API endpoints
export const assistantApi = {
  // Create a new conversation
  createConversation: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/assistant/conversations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error creating conversation:', error);
      throw error;
    }
  },
  
  // Send a message to the AI assistant
  sendMessage: async (conversationId, message) => {
    try {
      const response = await fetch(`${API_BASE_URL}/assistant/chat/${conversationId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content: message }),
      });
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  },
  
  // Get a conversation by ID
  getConversation: async (conversationId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/assistant/conversations/${conversationId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching conversation:', error);
      throw error;
    }
  },
};

export default {
  resumeApi,
  assistantApi,
};
