<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume Maker</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- React and ReactDOM -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <!-- Babel for JSX -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        .form-step {
            display: none;
        }
        .form-step.active {
            display: block;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto p-4 md:p-8">
        <h1 class="text-3xl font-bold text-blue-600 mb-4">Resume Maker</h1>

        <!-- Form Container -->
        <div class="bg-white p-6 rounded-lg shadow-md max-w-4xl mx-auto">
            <h2 class="text-2xl font-bold text-center mb-6">Create Your Resume</h2>

            <!-- Progress Bar -->
            <div class="w-full bg-gray-200 rounded-full h-2.5 mb-6">
                <div id="progress-bar" class="bg-blue-600 h-2.5 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>

            <!-- Step Indicator -->
            <div class="flex justify-between mb-8">
                <div id="step-number" class="text-sm">Step 1 of 6</div>
                <div id="step-title" class="text-sm font-medium">Personal Information</div>
            </div>

            <!-- Form Steps -->
            <form id="resume-form">
                <!-- Step 1: Personal Information -->
                <div id="step-1" class="form-step active">
                    <h3 class="text-lg font-semibold mb-4">Personal Information</h3>

                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="full_name">
                            Full Name <span class="text-red-500">*</span>
                        </label>
                        <input
                            type="text"
                            id="full_name"
                            name="full_name"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            placeholder="John Doe"
                            required
                        />
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="email">
                            Email <span class="text-red-500">*</span>
                        </label>
                        <input
                            type="email"
                            id="email"
                            name="email"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            placeholder="<EMAIL>"
                            required
                        />
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="phone">
                            Phone Number
                        </label>
                        <input
                            type="tel"
                            id="phone"
                            name="phone"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            placeholder="(*************"
                        />
                    </div>

                    <div class="mb-6">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="location">
                            Location
                        </label>
                        <input
                            type="text"
                            id="location"
                            name="location"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            placeholder="City, State"
                        />
                    </div>

                    <div class="flex justify-end">
                        <button
                            type="button"
                            onclick="nextStep()"
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                        >
                            Next
                        </button>
                    </div>
                </div>

                <!-- Step 2: Education -->
                <div id="step-2" class="form-step">
                    <h3 class="text-lg font-semibold mb-4">Education</h3>

                    <div id="education-list" class="mb-6">
                        <!-- Education items will be added here -->
                    </div>

                    <div class="mb-6 p-4 border rounded-md">
                        <h4 class="text-md font-medium mb-3">Add Education</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="institution">
                                    Institution <span class="text-red-500">*</span>
                                </label>
                                <input
                                    type="text"
                                    id="institution"
                                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                    placeholder="University Name"
                                />
                            </div>

                            <div>
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="degree">
                                    Degree <span class="text-red-500">*</span>
                                </label>
                                <input
                                    type="text"
                                    id="degree"
                                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                    placeholder="Bachelor of Science"
                                />
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="field_of_study">
                                Field of Study
                            </label>
                            <input
                                type="text"
                                id="field_of_study"
                                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                placeholder="Computer Science"
                            />
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="start_date">
                                    Start Date
                                </label>
                                <input
                                    type="month"
                                    id="start_date"
                                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                />
                            </div>

                            <div>
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="end_date">
                                    End Date
                                </label>
                                <input
                                    type="month"
                                    id="end_date"
                                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                />
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="edu_description">
                                Description
                            </label>
                            <textarea
                                id="edu_description"
                                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                rows="3"
                                placeholder="Describe your studies, achievements, etc."
                            ></textarea>
                        </div>

                        <button
                            type="button"
                            onclick="addEducation()"
                            class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                        >
                            Add Education
                        </button>
                    </div>

                    <div class="flex justify-between">
                        <button
                            type="button"
                            onclick="prevStep()"
                            class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                        >
                            Previous
                        </button>
                        <button
                            type="button"
                            onclick="nextStep()"
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                        >
                            Next
                        </button>
                    </div>
                </div>

                <!-- Step 3: Experience -->
                <div id="step-3" class="form-step">
                    <h3 class="text-lg font-semibold mb-4">Work Experience</h3>

                    <div id="experience-list" class="mb-6">
                        <!-- Experience items will be added here -->
                    </div>

                    <div class="mb-6 p-4 border rounded-md">
                        <h4 class="text-md font-medium mb-3">Add Experience</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="company">
                                    Company <span class="text-red-500">*</span>
                                </label>
                                <input
                                    type="text"
                                    id="company"
                                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                    placeholder="Company Name"
                                />
                            </div>

                            <div>
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="position">
                                    Position <span class="text-red-500">*</span>
                                </label>
                                <input
                                    type="text"
                                    id="position"
                                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                    placeholder="Job Title"
                                />
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="exp_start_date">
                                    Start Date
                                </label>
                                <input
                                    type="month"
                                    id="exp_start_date"
                                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                />
                            </div>

                            <div>
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="exp_end_date">
                                    End Date
                                </label>
                                <input
                                    type="month"
                                    id="exp_end_date"
                                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                />
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="exp_description">
                                Description
                            </label>
                            <textarea
                                id="exp_description"
                                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                rows="3"
                                placeholder="Describe your role and responsibilities"
                            ></textarea>
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2">
                                Achievements
                            </label>

                            <div class="flex mb-2">
                                <input
                                    type="text"
                                    id="achievement"
                                    class="shadow appearance-none border rounded-l w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                    placeholder="Add an achievement or key responsibility"
                                />
                                <button
                                    type="button"
                                    onclick="addAchievement()"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-r focus:outline-none focus:shadow-outline"
                                >
                                    Add
                                </button>
                            </div>

                            <ul id="achievements-list" class="list-disc pl-5 space-y-1">
                                <!-- Achievements will be added here -->
                            </ul>
                        </div>

                        <button
                            type="button"
                            onclick="addExperience()"
                            class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                        >
                            Add Experience
                        </button>
                    </div>

                    <div class="flex justify-between">
                        <button
                            type="button"
                            onclick="prevStep()"
                            class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                        >
                            Previous
                        </button>
                        <button
                            type="button"
                            onclick="nextStep()"
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                        >
                            Next
                        </button>
                    </div>
                </div>

                <!-- Step 4: Skills -->
                <div id="step-4" class="form-step">
                    <h3 class="text-lg font-semibold mb-4">Skills</h3>

                    <div id="skills-list" class="mb-6">
                        <!-- Skills will be added here -->
                    </div>

                    <div class="mb-6 p-4 border rounded-md">
                        <h4 class="text-md font-medium mb-3">Add Skill</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="skill_name">
                                    Skill Name <span class="text-red-500">*</span>
                                </label>
                                <input
                                    type="text"
                                    id="skill_name"
                                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                    placeholder="e.g., JavaScript, Project Management, etc."
                                />
                            </div>

                            <div>
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="skill_level">
                                    Proficiency Level
                                </label>
                                <select
                                    id="skill_level"
                                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                >
                                    <option value="Beginner">Beginner</option>
                                    <option value="Intermediate">Intermediate</option>
                                    <option value="Advanced">Advanced</option>
                                    <option value="Expert">Expert</option>
                                </select>
                            </div>
                        </div>

                        <button
                            type="button"
                            onclick="addSkill()"
                            class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                        >
                            Add Skill
                        </button>
                    </div>

                    <div class="flex justify-between">
                        <button
                            type="button"
                            onclick="prevStep()"
                            class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                        >
                            Previous
                        </button>
                        <button
                            type="button"
                            onclick="submitForm()"
                            class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                        >
                            Generate Resume
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Form data
        const formData = {
            full_name: '',
            email: '',
            phone: '',
            location: '',
            objective: '',
            summary: '',
            education: [],
            experience: [],
            skills: []
        };

        // Current step
        let currentStep = 1;
        const totalSteps = 4; // We're implementing 4 steps for simplicity

        // Update progress bar
        function updateProgress() {
            const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;
            document.getElementById('progress-bar').style.width = `${progress}%`;
            document.getElementById('step-number').textContent = `Step ${currentStep} of ${totalSteps}`;

            // Update step title
            const stepTitle = document.getElementById('step-title');
            if (currentStep === 1) stepTitle.textContent = 'Personal Information';
            else if (currentStep === 2) stepTitle.textContent = 'Education';
            else if (currentStep === 3) stepTitle.textContent = 'Work Experience';
            else if (currentStep === 4) stepTitle.textContent = 'Skills';
        }

        // Show current step
        function showStep(step) {
            // Hide all steps
            document.querySelectorAll('.form-step').forEach(el => {
                el.classList.remove('active');
            });

            // Show current step
            document.getElementById(`step-${step}`).classList.add('active');

            // Update progress
            updateProgress();
        }

        // Next step
        function nextStep() {
            // Validate current step
            if (currentStep === 1) {
                const fullName = document.getElementById('full_name').value;
                const email = document.getElementById('email').value;

                if (!fullName || !email) {
                    alert('Please fill in all required fields (Name and Email)');
                    return;
                }

                // Save data
                formData.full_name = fullName;
                formData.email = document.getElementById('email').value;
                formData.phone = document.getElementById('phone').value;
                formData.location = document.getElementById('location').value;
            }

            // If we're on the last step, add a summary and objective
            if (currentStep === totalSteps) {
                formData.summary = "Professional with experience in " +
                    formData.experience.map(exp => exp.company).join(", ") +
                    " and skills in " +
                    formData.skills.map(skill => skill.name).join(", ") + ".";

                formData.objective = "Seeking a challenging position where I can utilize my skills and experience.";
            }

            if (currentStep < totalSteps) {
                currentStep++;
                showStep(currentStep);
            }
        }

        // Previous step
        function prevStep() {
            if (currentStep > 1) {
                currentStep--;
                showStep(currentStep);
            }
        }

        // Add education
        function addEducation() {
            const institution = document.getElementById('institution').value;
            const degree = document.getElementById('degree').value;

            if (!institution || !degree) {
                alert('Please fill in at least the institution and degree fields');
                return;
            }

            const education = {
                institution,
                degree,
                field_of_study: document.getElementById('field_of_study').value,
                start_date: document.getElementById('start_date').value,
                end_date: document.getElementById('end_date').value,
                description: document.getElementById('edu_description').value
            };

            formData.education.push(education);

            // Add to list
            const educationList = document.getElementById('education-list');
            const educationItem = document.createElement('div');
            educationItem.className = 'p-3 border rounded-md bg-gray-50 mb-2';
            educationItem.innerHTML = `
                <div class="flex justify-between">
                    <div>
                        <p class="font-semibold">${education.degree}</p>
                        <p>${education.institution}</p>
                        ${education.start_date && education.end_date ?
                            `<p class="text-sm text-gray-600">${education.start_date} - ${education.end_date}</p>` : ''}
                    </div>
                    <button
                        type="button"
                        onclick="removeEducation(${formData.education.length - 1})"
                        class="text-red-500 hover:text-red-700"
                    >
                        Remove
                    </button>
                </div>
            `;
            educationList.appendChild(educationItem);

            // Clear form
            document.getElementById('institution').value = '';
            document.getElementById('degree').value = '';
            document.getElementById('field_of_study').value = '';
            document.getElementById('start_date').value = '';
            document.getElementById('end_date').value = '';
            document.getElementById('edu_description').value = '';
        }

        // Remove education
        function removeEducation(index) {
            formData.education.splice(index, 1);

            // Refresh list
            const educationList = document.getElementById('education-list');
            educationList.innerHTML = '';

            formData.education.forEach((education, i) => {
                const educationItem = document.createElement('div');
                educationItem.className = 'p-3 border rounded-md bg-gray-50 mb-2';
                educationItem.innerHTML = `
                    <div class="flex justify-between">
                        <div>
                            <p class="font-semibold">${education.degree}</p>
                            <p>${education.institution}</p>
                            ${education.start_date && education.end_date ?
                                `<p class="text-sm text-gray-600">${education.start_date} - ${education.end_date}</p>` : ''}
                        </div>
                        <button
                            type="button"
                            onclick="removeEducation(${i})"
                            class="text-red-500 hover:text-red-700"
                        >
                            Remove
                        </button>
                    </div>
                `;
                educationList.appendChild(educationItem);
            });
        }

        // Current achievements
        let currentAchievements = [];

        // Add achievement
        function addAchievement() {
            const achievement = document.getElementById('achievement').value;

            if (!achievement) return;

            currentAchievements.push(achievement);

            // Add to list
            const achievementsList = document.getElementById('achievements-list');
            const achievementItem = document.createElement('li');
            achievementItem.className = 'flex justify-between items-center';
            achievementItem.innerHTML = `
                <span>${achievement}</span>
                <button
                    type="button"
                    onclick="removeAchievement(${currentAchievements.length - 1})"
                    class="text-red-500 hover:text-red-700 text-sm"
                >
                    Remove
                </button>
            `;
            achievementsList.appendChild(achievementItem);

            // Clear input
            document.getElementById('achievement').value = '';
        }

        // Remove achievement
        function removeAchievement(index) {
            currentAchievements.splice(index, 1);

            // Refresh list
            const achievementsList = document.getElementById('achievements-list');
            achievementsList.innerHTML = '';

            currentAchievements.forEach((achievement, i) => {
                const achievementItem = document.createElement('li');
                achievementItem.className = 'flex justify-between items-center';
                achievementItem.innerHTML = `
                    <span>${achievement}</span>
                    <button
                        type="button"
                        onclick="removeAchievement(${i})"
                        class="text-red-500 hover:text-red-700 text-sm"
                    >
                        Remove
                    </button>
                `;
                achievementsList.appendChild(achievementItem);
            });
        }

        // Add experience
        function addExperience() {
            const company = document.getElementById('company').value;
            const position = document.getElementById('position').value;

            if (!company || !position) {
                alert('Please fill in at least the company and position fields');
                return;
            }

            const experience = {
                company,
                position,
                start_date: document.getElementById('exp_start_date').value,
                end_date: document.getElementById('exp_end_date').value,
                description: document.getElementById('exp_description').value,
                achievements: [...currentAchievements]
            };

            formData.experience.push(experience);

            // Add to list
            const experienceList = document.getElementById('experience-list');
            const experienceItem = document.createElement('div');
            experienceItem.className = 'p-3 border rounded-md bg-gray-50 mb-2';
            experienceItem.innerHTML = `
                <div class="flex justify-between">
                    <div>
                        <p class="font-semibold">${experience.position}</p>
                        <p>${experience.company}</p>
                        ${experience.start_date && experience.end_date ?
                            `<p class="text-sm text-gray-600">${experience.start_date} - ${experience.end_date}</p>` : ''}
                    </div>
                    <button
                        type="button"
                        onclick="removeExperience(${formData.experience.length - 1})"
                        class="text-red-500 hover:text-red-700"
                    >
                        Remove
                    </button>
                </div>
            `;
            experienceList.appendChild(experienceItem);

            // Clear form
            document.getElementById('company').value = '';
            document.getElementById('position').value = '';
            document.getElementById('exp_start_date').value = '';
            document.getElementById('exp_end_date').value = '';
            document.getElementById('exp_description').value = '';
            currentAchievements = [];
            document.getElementById('achievements-list').innerHTML = '';
        }

        // Remove experience
        function removeExperience(index) {
            formData.experience.splice(index, 1);

            // Refresh list
            const experienceList = document.getElementById('experience-list');
            experienceList.innerHTML = '';

            formData.experience.forEach((experience, i) => {
                const experienceItem = document.createElement('div');
                experienceItem.className = 'p-3 border rounded-md bg-gray-50 mb-2';
                experienceItem.innerHTML = `
                    <div class="flex justify-between">
                        <div>
                            <p class="font-semibold">${experience.position}</p>
                            <p>${experience.company}</p>
                            ${experience.start_date && experience.end_date ?
                                `<p class="text-sm text-gray-600">${experience.start_date} - ${experience.end_date}</p>` : ''}
                        </div>
                        <button
                            type="button"
                            onclick="removeExperience(${i})"
                            class="text-red-500 hover:text-red-700"
                        >
                            Remove
                        </button>
                    </div>
                `;
                experienceList.appendChild(experienceItem);
            });
        }

        // Submit form
        async function submitForm() {
            try {
                const response = await fetch('http://localhost:8000/api/resume/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData),
                });

                if (!response.ok) {
                    throw new Error('Failed to generate resume');
                }

                const data = await response.json();
                console.log('Resume generated:', data);

                alert('Resume created successfully!');

            } catch (error) {
                console.error('Error generating resume:', error);
                alert('Failed to create resume. Please try again.');
            }
        }

        // Add skill
        function addSkill() {
            const skillName = document.getElementById('skill_name').value;

            if (!skillName) {
                alert('Please enter a skill name');
                return;
            }

            const skill = {
                name: skillName,
                level: document.getElementById('skill_level').value
            };

            formData.skills.push(skill);

            // Add to list
            const skillsList = document.getElementById('skills-list');
            const skillItem = document.createElement('div');
            skillItem.className = 'p-3 border rounded-md bg-gray-50 mb-2';
            skillItem.innerHTML = `
                <div class="flex justify-between items-center">
                    <div>
                        <p class="font-semibold">${skill.name}</p>
                        <p class="text-sm text-gray-600">${skill.level}</p>
                    </div>
                    <button
                        type="button"
                        onclick="removeSkill(${formData.skills.length - 1})"
                        class="text-red-500 hover:text-red-700"
                    >
                        Remove
                    </button>
                </div>
            `;
            skillsList.appendChild(skillItem);

            // Clear form
            document.getElementById('skill_name').value = '';
            document.getElementById('skill_level').value = 'Beginner';
        }

        // Remove skill
        function removeSkill(index) {
            formData.skills.splice(index, 1);

            // Refresh list
            const skillsList = document.getElementById('skills-list');
            skillsList.innerHTML = '';

            formData.skills.forEach((skill, i) => {
                const skillItem = document.createElement('div');
                skillItem.className = 'p-3 border rounded-md bg-gray-50 mb-2';
                skillItem.innerHTML = `
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="font-semibold">${skill.name}</p>
                            <p class="text-sm text-gray-600">${skill.level}</p>
                        </div>
                        <button
                            type="button"
                            onclick="removeSkill(${i})"
                            class="text-red-500 hover:text-red-700"
                        >
                            Remove
                        </button>
                    </div>
                `;
                skillsList.appendChild(skillItem);
            });
        }

        // Initialize
        showStep(currentStep);
    </script>
</body>
</html>
